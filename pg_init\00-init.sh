#!/bin/bash
# This script is executed automatically by PostgreSQL when the container starts

set -e

echo "Running PostgreSQL initialization script..."

# Create extensions
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    CREATE EXTENSION IF NOT EXISTS "pgcrypto";
EOSQL

echo "Extensions created successfully."

# Check if tables already exist
TABLE_COUNT=$(psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'")

if [ "$TABLE_COUNT" -gt "0" ]; then
    echo "Database already contains tables. Skipping initialization."
else
    echo "Initializing database with schema..."
    # Run the create-tables.sql script
    psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" -f /docker-entrypoint-initdb.d/create-tables.sql
    echo "Database initialization completed successfully."
fi

# List created tables
echo "Tables in database:"
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" -c "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name"

echo "PostgreSQL initialization completed."
