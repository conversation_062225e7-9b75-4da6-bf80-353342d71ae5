-- PHASE 1: Critical Performance Indexes for TempFly.io API
-- These indexes are designed to optimize the most common query patterns
-- Execute this file to add performance-critical composite indexes

-- Enable concurrent index creation to avoid blocking operations
SET maintenance_work_mem = '256MB';

-- Critical composite index for inbox queries by RapidAPI key
-- Optimizes: SELECT COUNT(*) FROM inboxes WHERE rapidapi_key = ? AND is_active = true AND (expiry_date IS NULL OR expiry_date > NOW())
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inboxes_composite 
ON inboxes(rapidapi_key, is_active, expiry_date);

-- Critical composite index for email filtering queries
-- Optimizes: SELECT * FROM emails WHERE inbox_id = ? AND from_address ILIKE ? ORDER BY received_at
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_emails_inbox_filters 
ON emails(inbox_id, from_address, received_at);

-- Full-text search index for email from_address filtering
-- Optimizes: SELECT * FROM emails WHERE to_tsvector('english', from_address) @@ plainto_tsquery('english', ?)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_emails_from_address_gin 
ON emails USING gin(to_tsvector('english', from_address));

-- Additional composite index for email pagination with filters
-- Optimizes: SELECT * FROM emails WHERE inbox_id = ? ORDER BY received_at DESC LIMIT ? OFFSET ?
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_emails_inbox_received_desc 
ON emails(inbox_id, received_at DESC);

-- Composite index for active inbox lookups
-- Optimizes: SELECT * FROM inboxes WHERE is_active = true AND (expiry_date IS NULL OR expiry_date > NOW())
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inboxes_active_expiry 
ON inboxes(is_active, expiry_date) WHERE is_active = true;

-- Analyze tables after index creation to update statistics
ANALYZE inboxes;
ANALYZE emails;

-- Reset maintenance_work_mem to default
RESET maintenance_work_mem;
