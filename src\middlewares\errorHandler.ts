import { Request, Response, NextFunction } from 'express';
import serverConfig from '../config/server';
import logger from '../utils/logger';
import auditLogger from '../utils/auditLogger';
import healthCheck from '../utils/healthCheck';
import { v4 as uuidv4 } from 'uuid';

// Custom error class
export class AppError extends Error {
  statusCode: number;
  isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Factory methods for common error types
export const createError = {
  // 400 Bad Request - Invalid input
  badRequest: (message: string) => new AppError(message, 400),

  // 401 Unauthorized - Authentication failure
  unauthorized: (message = 'Authentication required') => new AppError(message, 401),

  // 403 Forbidden - Permission denied
  forbidden: (message = 'Permission denied') => new AppError(message, 403),

  // 404 Not Found - Resource not found
  notFound: (resource = 'Resource') => new AppError(`${resource} not found`, 404),

  // 409 Conflict - Resource conflict
  conflict: (message: string) => new AppError(message, 409),

  // 422 Unprocessable Entity - Validation error
  validation: (message: string) => new AppError(message, 422),

  // 429 Too Many Requests - Rate limit exceeded
  rateLimit: (message = 'Rate limit exceeded') => new AppError(message, 429),

  // 500 Internal Server Error - Server error
  internal: (message = 'Internal Server Error') => {
    const error = new AppError(message, 500);
    error.isOperational = false; // Mark as non-operational
    return error;
  },

  // 503 Service Unavailable - Service unavailable
  serviceUnavailable: (message = 'Service temporarily unavailable') => new AppError(message, 503),
}

// Error handler middleware
export const errorHandler = (
  err: Error | AppError,
  req: Request,
  res: Response,
  _next: NextFunction
) => {
  // Default error status and message
  let statusCode = 500;
  let message = 'Internal Server Error';
  let stack: string | undefined;

  // If it's our custom error, use its status code and message
  if (err instanceof AppError) {
    statusCode = err.statusCode;
    // For operational errors (like 400, 401, 404), we can show the actual message
    if (err.isOperational && statusCode < 500) {
      message = err.message;
    }
  } else if (err instanceof Error) {
    // For non-AppError instances, always use generic message for 500 errors
    // but log the actual error message for debugging
    message = 'Internal Server Error';
  }

  // Include stack trace in development mode
  if (serverConfig.isDevelopment) {
    stack = err.stack;
  }

  // Get request ID if available or generate a new one
  const requestId = (req as any).requestId || uuidv4().toUpperCase();

  // Log the error with request ID
  logger.error(`${statusCode} - ${message}`, err, requestId);

  // Log to audit system
  auditLogger.logError(req, err, statusCode);

  // Increment error counter for monitoring
  healthCheck.incrementErrorCount();

  // Create a sanitized error response in RapidAPI format
  const errorResponse = {
    'request-id': requestId,
    'message': message,
    'data': null,
    'code': statusCode
  };

  // In development mode, include additional debugging information
  // IMPORTANT: This should never be enabled in production
  if (serverConfig.isDevelopment && process.env.NODE_ENV !== 'production') {
    // Include the actual error message even for 500 errors in development
    if (statusCode >= 500) {
      (errorResponse as any).actualError = err.message;
    }

    // Include stack trace if available
    if (stack) {
      (errorResponse as any).stack = stack;
    }
  }

  // Always log the full error details for debugging, but never expose in response
  if (statusCode >= 500) {
    // Log error details with the error object
    logger.error(`FULL ERROR DETAILS: ${err.message}`, err);

    // Log request info separately
    logger.error(`Request info - Path: ${req.path}, Method: ${req.method}`);
  }

  // Send the error response
  res.status(statusCode).json(errorResponse);
};

// Common scanning patterns to reduce logging noise
const COMMON_SCAN_PATTERNS = [
  '.env',
  '.git',
  'wp-',
  'admin',
  'config',
  'phpinfo',
  'credentials',
  'aws',
  'laravel',
  'api/swagger',
  'api/docs',
  'actuator',
  'prometheus',
  'metrics',
  'health',
  'status',
  'debug',
  'console',
  'shell',
  'cmd',
  'cgi-bin',
  'phpmyadmin',
  'mysql',
  'db',
  'database',
  'backup',
  'wp-content',
  'wp-admin',
  'wp-login',
  'xmlrpc.php',
  'robots.txt',
  'sitemap.xml',
  'favicon.ico'
];

// Track 404 counts to reduce logging
const notFoundCounts: Record<string, number> = {};
let lastLogTime = Date.now();
const LOG_INTERVAL = 60000; // Log summary every minute

// Not found middleware
export const notFoundHandler = (req: Request, _res: Response, next: NextFunction) => {
  const path = req.originalUrl;

  // Check if this is a common scanning pattern
  const isCommonScan = COMMON_SCAN_PATTERNS.some(pattern => path.includes(pattern));

  // For common scanning patterns, use minimal logging
  if (isCommonScan) {
    // Increment the count for this path
    notFoundCounts[path] = (notFoundCounts[path] || 0) + 1;

    // Log a summary periodically instead of every request
    const now = Date.now();
    if (now - lastLogTime > LOG_INTERVAL) {
      // Log summary of 404s
      const totalPaths = Object.keys(notFoundCounts).length;
      const totalRequests = Object.values(notFoundCounts).reduce((sum, count) => sum + count, 0);

      if (totalRequests > 0) {
        logger.warn(`404 Summary: ${totalRequests} requests for ${totalPaths} unique paths in the last minute`);

        // Log the top 5 most requested paths
        const topPaths = Object.entries(notFoundCounts)
          .sort((a, b) => b[1] - a[1])
          .slice(0, 5);

        if (topPaths.length > 0) {
          logger.warn(`Top 404 paths: ${topPaths.map(([path, count]) => `${path} (${count})`).join(', ')}`);
        }

        // Reset counts
        for (const key in notFoundCounts) {
          notFoundCounts[key] = 0;
        }
      }

      lastLogTime = now;
    }
  } else {
    // For non-scanning 404s, log normally
    logger.error(`404 - Route: ${path} not found`);
  }

  const err = createError.notFound(`Route: ${path}`);
  next(err);
};
