-- Setup pg_cron extension for scheduled tasks
-- This script installs pg_cron and sets up scheduled jobs for inbox cleanup

-- Create the pg_cron extension if it doesn't exist
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Grant usage on the pg_cron schema to the current user
GRANT USAGE ON SCHEMA cron TO CURRENT_USER;

-- Create a function to clean up expired inboxes
CREATE OR REPLACE FUNCTION cleanup_expired_inboxes()
RETURNS INTEGER AS $$
DECLARE
    affected_rows INTEGER;
    start_time TIMESTAMP WITH TIME ZONE;
    end_time TIMESTAMP WITH TIME ZONE;
    execution_time INTEGER;
BEGIN
    start_time := clock_timestamp();
    
    -- Mark expired inboxes as inactive
    WITH updated_inboxes AS (
        UPDATE inboxes
        SET is_active = false
        WHERE expiry_date IS NOT NULL
        AND expiry_date < NOW()
        AND is_active = true
        RETURNING id
    )
    SELECT COUNT(*) INTO affected_rows FROM updated_inboxes;
    
    end_time := clock_timestamp();
    execution_time := EXTRACT(EPOCH FROM (end_time - start_time)) * 1000; -- Convert to milliseconds
    
    -- Log the operation to maintenance_logs
    IF affected_rows > 0 THEN
        INSERT INTO maintenance_logs (
            operation, 
            details, 
            affected_rows, 
            execution_time, 
            status
        )
        VALUES (
            'cleanup_expired_inboxes_cron',
            'Scheduled cleanup of expired inboxes via pg_cron',
            affected_rows,
            execution_time,
            'success'
        );
    END IF;
    
    RETURN affected_rows;
EXCEPTION WHEN OTHERS THEN
    -- Log error to maintenance_logs
    INSERT INTO maintenance_logs (
        operation, 
        details, 
        affected_rows, 
        execution_time, 
        status, 
        error_message
    )
    VALUES (
        'cleanup_expired_inboxes_cron',
        'Scheduled cleanup of expired inboxes via pg_cron failed',
        0,
        NULL,
        'error',
        SQLERRM
    );
    
    -- Re-raise the exception
    RAISE;
END;
$$ LANGUAGE plpgsql;

-- Schedule the cleanup job to run every 15 minutes
SELECT cron.schedule(
    'cleanup-expired-inboxes',
    '*/15 * * * *',  -- Every 15 minutes
    $$SELECT cleanup_expired_inboxes()$$
);

-- Create a function to purge old maintenance logs
CREATE OR REPLACE FUNCTION purge_old_maintenance_logs()
RETURNS INTEGER AS $$
DECLARE
    affected_rows INTEGER;
BEGIN
    -- Delete maintenance logs older than 30 days
    DELETE FROM maintenance_logs
    WHERE executed_at < NOW() - INTERVAL '30 days';
    
    GET DIAGNOSTICS affected_rows = ROW_COUNT;
    
    -- Log the purge operation
    IF affected_rows > 0 THEN
        INSERT INTO maintenance_logs (
            operation, 
            details, 
            affected_rows
        )
        VALUES (
            'purge_old_maintenance_logs',
            'Purged maintenance logs older than 30 days',
            affected_rows
        );
    END IF;
    
    RETURN affected_rows;
END;
$$ LANGUAGE plpgsql;

-- Schedule the log purging job to run daily at 3:30 AM
SELECT cron.schedule(
    'purge-old-maintenance-logs',
    '30 3 * * *',  -- At 3:30 AM every day
    $$SELECT purge_old_maintenance_logs()$$
);

-- List all scheduled jobs for verification
SELECT * FROM cron.job;
