-- Migration script to standardize on the 'name' column in the inboxes table
-- This script:
-- 1. Updates any NULL name values with username values
-- 2. Creates an index on the name column for better performance
-- 3. Adds a comment to the table explaining the standardization

-- Start transaction
BEGIN;

-- Step 1: Update any NULL name values with username values
UPDATE inboxes
SET name = username
WHERE name IS NULL AND username IS NOT NULL;

-- Step 2: Create an index on the name column for better performance with name filtering
CREATE INDEX IF NOT EXISTS idx_inboxes_name ON inboxes(name);

-- Step 3: Add a comment to the table explaining the standardization
COMMENT ON COLUMN inboxes.name IS 'The standardized name of the inbox (local part of the email address)';
COMMENT ON COLUMN inboxes.username IS 'DEPRECATED: Use name column instead. Kept for backward compatibility.';

-- Step 4: Add a trigger to keep username in sync with name for backward compatibility
CREATE OR REPLACE FUNCTION sync_username_with_name()
RETURNS TRIGGER AS $$
BEGIN
    NEW.username := NEW.name;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger 
        WHERE tgname = 'sync_username_with_name_trigger'
    ) THEN
        CREATE TRIGGER sync_username_with_name_trigger
        BEFORE INSERT OR UPDATE ON inboxes
        FOR EACH ROW
        EXECUTE FUNCTION sync_username_with_name();
    END IF;
END $$;

-- Commit transaction
COMMIT;

-- Verify the changes
SELECT 
    COUNT(*) AS total_inboxes,
    COUNT(name) AS inboxes_with_name,
    COUNT(username) AS inboxes_with_username,
    COUNT(*) FILTER (WHERE name = username) AS inboxes_with_matching_values
FROM inboxes;
