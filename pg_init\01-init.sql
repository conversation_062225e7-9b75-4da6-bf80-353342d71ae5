-- PostgreSQL initialization script for TempFly.io API
-- This script will be executed when the PostgreSQL container is first created

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Set optimal PostgreSQL parameters
ALTER SYSTEM SET shared_buffers = '2GB';
ALTER SYSTEM SET effective_cache_size = '2GB';
ALTER SYSTEM SET maintenance_work_mem = '512MB';
ALTER SYSTEM SET work_mem = '16MB';
ALTER SYSTEM SET max_connections = '200';
ALTER SYSTEM SET max_worker_processes = '6';
ALTER SYSTEM SET max_parallel_workers = '6';
ALTER SYSTEM SET max_parallel_workers_per_gather = '3';
ALTER SYSTEM SET random_page_cost = '1.1';
ALTER SYSTEM SET effective_io_concurrency = '200';
ALTER SYSTEM SET checkpoint_completion_target = '0.9';
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = '100';
ALTER SYSTEM SET autovacuum = 'on';

-- Create schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS public;

-- Set search path
SET search_path TO public;

-- Create indexes on commonly queried columns
CREATE INDEX IF NOT EXISTS idx_inboxes_address ON inboxes(address);
CREATE INDEX IF NOT EXISTS idx_inboxes_rapidapi_key ON inboxes(rapidapi_key);
CREATE INDEX IF NOT EXISTS idx_emails_inbox_id ON emails(inbox_id);
CREATE INDEX IF NOT EXISTS idx_emails_created_at ON emails(created_at);

-- Optimize vacuum settings for high-traffic tables
ALTER TABLE inboxes SET (autovacuum_vacuum_scale_factor = 0.05);
ALTER TABLE emails SET (autovacuum_vacuum_scale_factor = 0.05);
ALTER TABLE domains SET (autovacuum_vacuum_scale_factor = 0.1);

-- Set appropriate fillfactor for frequently updated tables
ALTER TABLE inboxes SET (fillfactor = 80);
ALTER TABLE emails SET (fillfactor = 80);

-- Enable parallel query for better performance
ALTER TABLE inboxes SET (parallel_workers = 3);
ALTER TABLE emails SET (parallel_workers = 3);
ALTER TABLE domains SET (parallel_workers = 2);
