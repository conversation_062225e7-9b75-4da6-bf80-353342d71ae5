-- PHASE 2: Additional Performance Indexes for TempFly.io API
-- These indexes are designed to optimize pagination and email retrieval patterns
-- Execute this file to add pagination-specific indexes

-- Enable concurrent index creation to avoid blocking operations
SET maintenance_work_mem = '256MB';

-- Critical pagination index for email queries with ordering
-- Optimizes: SELECT * FROM emails WHERE inbox_id = ? ORDER BY received_at DESC LIMIT ? OFFSET ?
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_emails_inbox_received_pagination 
ON emails(inbox_id, received_at DESC, id);

-- Additional index for email filtering with pagination
-- Optimizes: SELECT * FROM emails WHERE inbox_id = ? AND from_address ILIKE ? ORDER BY received_at DESC
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_emails_inbox_from_received 
ON emails(inbox_id, from_address, received_at DESC);

-- Index for inbox count queries (already exists but ensuring it's optimal)
-- Optimizes: SELECT COUNT(*) FROM inboxes WHERE rapidapi_key = ? AND is_active = true
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inboxes_rapidapi_count 
ON inboxes(rapidapi_key, is_active) 
WHERE rapidapi_key IS NOT NULL AND is_active = true;

-- Analyze tables after index creation to update statistics
ANALYZE emails;
ANALYZE inboxes;

-- Reset maintenance_work_mem to default
RESET maintenance_work_mem;

-- Log completion
SELECT 'PHASE 2: Pagination indexes created successfully' AS status;
