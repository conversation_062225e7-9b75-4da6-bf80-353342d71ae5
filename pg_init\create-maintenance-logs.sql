-- Create maintenance_logs table for tracking system maintenance operations
CREATE TABLE IF NOT EXISTS maintenance_logs (
    id SERIAL PRIMARY KEY,
    operation VARCHAR(255) NOT NULL,
    details TEXT,
    affected_rows INTEGER,
    executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    execution_time INTEGER, -- in milliseconds
    status VARCHAR(50) DEFAULT 'success',
    error_message TEXT
);

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_maintenance_logs_operation ON maintenance_logs(operation);
CREATE INDEX IF NOT EXISTS idx_maintenance_logs_executed_at ON maintenance_logs(executed_at);

-- Add comment to explain the table's purpose
COMMENT ON TABLE maintenance_logs IS 'Tracks system maintenance operations like cleanup jobs and scheduled tasks';
