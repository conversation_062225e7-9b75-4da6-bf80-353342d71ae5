-- Optimization for inbox count queries used in free user limit enforcement
-- This script creates a composite index to speed up the inbox count query

-- Create a composite index specifically for the free user inbox count query
-- This index covers all columns used in the WHERE clause of countActiveInboxesByRapidApiKey
CREATE INDEX IF NOT EXISTS idx_inboxes_rapidapi_active_expiry 
ON inboxes(rapidapi_key, is_active, expiry_date) 
WHERE rapidapi_key IS NOT NULL AND is_active = true;

-- Create a partial index for active inboxes with no expiry date
CREATE INDEX IF NOT EXISTS idx_inboxes_rapidapi_active_no_expiry 
ON inboxes(rapidapi_key, is_active) 
WHERE rapidapi_key IS NOT NULL AND is_active = true AND expiry_date IS NULL;

-- Create a partial index for active inboxes with future expiry dates
CREATE INDEX IF NOT EXISTS idx_inboxes_rapidapi_active_future_expiry 
ON inboxes(rapidapi_key, is_active, expiry_date) 
WHERE rapidapi_key IS NOT NULL AND is_active = true AND expiry_date > NOW();

-- Analyze the table to update statistics for the query planner
ANALYZE inboxes;

-- Show the query plan for the inbox count query (for debugging)
-- EXPLAIN (ANALYZE, BUFFERS) 
-- SELECT COUNT(*) FROM inboxes 
-- WHERE rapidapi_key = 'test-key' 
-- AND is_active = true 
-- AND (expiry_date IS NULL OR expiry_date > NOW());
