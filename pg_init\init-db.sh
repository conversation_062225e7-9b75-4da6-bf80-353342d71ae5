#!/bin/bash
# Database initialization script for TempFly.io

set -e

# Wait for PostgreSQL to be ready
until PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -U "$POSTGRES_USER" -d "$POSTGRES_DB" -c '\q'; do
  >&2 echo "PostgreSQL is unavailable - sleeping"
  sleep 1
done

>&2 echo "PostgreSQL is up - executing initialization scripts"

# Run the table creation script
PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -U "$POSTGRES_USER" -d "$POSTGRES_DB" -f /docker-entrypoint-initdb.d/create-tables.sql

# Create maintenance_logs table
>&2 echo "Creating maintenance_logs table..."
PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -U "$POSTGRES_USER" -d "$POSTGRES_DB" -f /docker-entrypoint-initdb.d/create-maintenance-logs.sql

# Setup pg_cron for scheduled tasks
>&2 echo "Setting up pg_cron extension and scheduled jobs..."
PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -U "$POSTGRES_USER" -d "$POSTGRES_DB" -f /docker-entrypoint-initdb.d/setup-pg-cron.sql

# Confirm tables were created
PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -U "$POSTGRES_USER" -d "$POSTGRES_DB" -c "SELECT table_name FROM information_schema.tables WHERE table_schema='public';"

# Confirm pg_cron jobs were created
>&2 echo "Scheduled pg_cron jobs:"
PGPASSWORD=$POSTGRES_PASSWORD psql -h localhost -U "$POSTGRES_USER" -d "$POSTGRES_DB" -c "SELECT jobid, jobname, schedule, command FROM cron.job;"

>&2 echo "Database initialization completed successfully"
