# TempFly.io API - Eu VPS Configuration
#
# This configuration is optimized for Eu VPS with:
# - 2 shared vCPUs
# - 8GB RAM
# - 256GB SSD storage
#
# Resource allocation strategy:
# - API Service: 1 instance with 2 CPUs and 3GB RAM
# - PostgreSQL: 2 CPU and 3GB RAM
# - Redis: 1 CPU and 2GB RAM
# - Reserved for system: ~0.5GB RAM
#
# This configuration is designed to work with Coolify as the deployment platform.

#version: "3.9"

services:
  # Single API Service Instance (CockroachDB Configuration)
  app1:
    build: .
    env_file: .env
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 3g
    expose:
      - "3000"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/healthz"]
      interval: 20s
      timeout: 10s
      retries: 3
      start_period: 30s
    environment:
      - NODE_ENV=production
      - PORT=3000
      - INSTANCE_ID=1
      # CockroachDB database connection settings
      - PGHOST=${PGHOST}
      - PGPORT=${PGPORT}
      - PGDATABASE=${PGDATABASE}
      - PGUSER=${PGUSER}
      - PGPASSWORD=${PGPASSWORD}
      - PGSSLMODE=verify-full
      - DATABASE_URL=${DATABASE_URL}
      # Connection pool settings for CockroachDB
      - PG_MAX_CONNECTIONS=15
      - PG_MIN_CONNECTIONS=3
      - PG_IDLE_TIMEOUT=30000
      # Redis connection (local)
      - REDIS_ENABLED=true
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DEFAULT_TTL=7200
      # Performance optimizations
      - CONNECTION_WARMUP_INTERVAL=45000
      - MEMORY_CACHE_SIZE=200
      - MAX_MEMORY_CACHE_ITEMS=1000
      # API settings
      - API_URL=${API_URL}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - RAPIDAPI_PROXY_SECRET=${RAPIDAPI_PROXY_SECRET}
    volumes:
      - type: bind
        source: ./logs
        target: /app/logs
    depends_on:
      - redis
    networks:
      - tempmail-network



  # Redis Cache (smaller allocation)
  redis:
    image: redis:7-alpine
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: 1g
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf", "--requirepass", "${REDIS_PASSWORD}"]
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    networks:
      - tempmail-network

volumes:
  redis_data:

networks:
  tempmail-network:
    driver: bridge
