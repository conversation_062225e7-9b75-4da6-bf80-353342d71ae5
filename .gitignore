# Dependency directories
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log

# Build output
dist/
build/
out/
.next/

# Environment variables
.env
.env.new
.env.example
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Log rotation files
logs/*.log.*
*.log.gz
*.log.[0-9]*

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# IDE files
.vscode/
.idea/
*.swp
*.swo
*.sublime-workspace
*.sublime-project

# OS files
.DS_Store
Thumbs.db
.directory
Desktop.ini

# Temporary files
*.tmp
*.temp
.cache/
temp/
tmp/

# API keys and secrets
*.pem
*.key
*.cert

.env.*
env.json


# Database files
*.sqlite
*.sqlite3
*.db
Database.md

# Generated files
generated/

# Local development files
.vercel
.netlify

# Redis dump file
dump.rdb


# Test Scripts
test-scripts/

# Development and testing scripts
/scripts/*


openapi.yaml
ope



/api-template


fix-tls-reject.js
cloudflare-ssl-guide.md
fix-attachments-table.sql
run-sql-fix.js

# Root directory utility scripts - potential security risks
/run-migration.js

/create-*-api-key.js
/list-api-keys.js
/remove-dummy-domains.js
/comprehensive-test.js
/test-all-endpoints.js
/configure-dns.js

# Security-sensitive scripts
**/generate-api-key*.js
**/create-admin*.js
**/reset-password*.js
**/bypass-*.js
**/auth-test*.js
**/credentials*.js
**/secrets*.js
**/token*.js
**/config-*.js
**/setup-*.js
**/deploy-*.js
**/generate-encryption-key.js

# Database migrations (already applied to remote database)
/src/migrations/

# Temporary development files
.vscode-test/
.nyc_output/
.eslintcache
.stylelintcache

# Performance testing files
profile-*.json
heap-*.json
flamegraph-*.html
test-*.js


# Local SSL certificates
/ssl/
/certs/

# Backup files
*.bak
*.backup
*.old
*.orig

*.md
*.txt
*.json

# Monitoring and metrics files
/metrics/
/monitoring/

# Local development configuration
/config/local.*
/config/dev.*

# Temporary test data
/test-data/
/mock-data/
postman_collection.json
response.json
migrations
docs
comprehensive-test.js

# RapidAPI documentation
rapidapi.md

migrate.js

DOCKER_README.md
COOLIFY.md

.env.docker.example

docker-compose.dev.yaml