import { Request, Response, NextFunction } from 'express';
import { ApiKeyModel } from '../models/ApiKey';
import { AppError } from '../middlewares/errorHandler';

/**
 * Create a new API key
 */
export const createApiKey = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { name, expiresIn } = req.body;

    // Validate name
    if (!name || typeof name !== 'string') {
      return next(new AppError('Name is required and must be a string', 400));
    }

    // Calculate expiration date if expiresIn is provided (in days)
    let expiresAt: Date | null = null;
    if (expiresIn && typeof expiresIn === 'number' && expiresIn > 0) {
      expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + expiresIn);
    }

    // Create API key
    const apiKey = await ApiKeyModel.create(name, expiresAt);

    res.status(201).json({
      status: 'success',
      data: {
        apiKey: {
          id: apiKey.id,
          key: apiKey.key,
          name: apiKey.name,
          created_at: apiKey.created_at,
          expires_at: apiKey.expires_at,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * List all API keys
 */
export const listApiKeys = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const size = parseInt(req.query.size as string) || 10;

    // Validate pagination parameters
    if (page < 1 || size < 1 || size > 100) {
      return next(
        new AppError(
          'Invalid pagination parameters. Page must be >= 1 and size must be between 1 and 100',
          400
        )
      );
    }

    const { apiKeys, total } = await ApiKeyModel.list(page, size);

    res.status(200).json({
      status: 'success',
      data: {
        apiKeys: apiKeys.map((apiKey) => ({
          id: apiKey.id,
          name: apiKey.name,
          created_at: apiKey.created_at,
          expires_at: apiKey.expires_at,
          last_used_at: apiKey.last_used_at,
          usage_count: apiKey.usage_count,
        })),
        pagination: {
          page,
          size,
          total,
          pages: Math.ceil(total / size),
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Revoke an API key
 */
export const revokeApiKey = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { id } = req.params;

    if (!id) {
      return next(new AppError('API key ID is required', 400));
    }

    const apiKey = await ApiKeyModel.getById(id);
    if (!apiKey) {
      return next(new AppError('API key not found', 404));
    }

    await ApiKeyModel.deactivate(id);

    res.status(200).json({
      status: 'success',
      message: 'API key revoked successfully',
    });
  } catch (error) {
    next(error);
  }
};
