FROM postgres:15

RUN apt-get update && \
    apt-get install -y git make gcc clang llvm postgresql-server-dev-15 postgresql-15-pglogical && \
    rm -rf /var/lib/apt/lists/*

# Optional: Install pg_cron if you still need it
RUN git clone --branch v1.5.2 --depth 1 https://github.com/citusdata/pg_cron.git /tmp/pg_cron && \
    cd /tmp/pg_cron && \
    make && make install && \
    rm -rf /tmp/pg_cron

# Copy any initialization scripts
COPY pg_init /docker-entrypoint-initdb.d/

EXPOSE 5432

CMD ["postgres", \
     "-c", "shared_buffers=2GB", \
     "-c", "effective_cache_size=4GB", \
     "-c", "maintenance_work_mem=512MB", \
     "-c", "work_mem=16MB", \
     "-c", "max_connections=200", \
     "-c", "max_worker_processes=6", \
     "-c", "max_parallel_workers=6", \
     "-c", "max_parallel_workers_per_gather=3", \
     "-c", "random_page_cost=1.1", \
     "-c", "effective_io_concurrency=200", \
     "-c", "checkpoint_completion_target=0.9", \
     "-c", "wal_buffers=16MB", \
     "-c", "default_statistics_target=100", \
     "-c", "autovacuum=on", \
     "-c", "shared_preload_libraries=pg_cron,pglogical", \
     "-c", "cron.database_name=tempmail"]
