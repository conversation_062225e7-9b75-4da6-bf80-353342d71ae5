import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { RapidApiUsageModel } from '../models/RapidApiUsage';
import logger from '../utils/logger';

/**
 * Get RapidAPI usage for the current user
 */
export const getUserUsage = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const requestId = req.headers['x-request-id'] || uuidv4();

    // Ensure this is a RapidAPI request
    if (!(req as any).rapidApi || !(req as any).rapidApi.user) {
      res.status(400).json({
        'request-id': requestId,
        'message': 'This endpoint is only available for RapidAPI users',
        'data': null,
        'code': 400
      });
      return;
    }

    // Get usage summary for the current user
    const usageSummary = await RapidApiUsageModel.getUserSummary((req as any).rapidApi.user);

    if (!usageSummary) {
      res.status(404).json({
        'request-id': requestId,
        'message': 'No usage data found for this user',
        'data': null,
        'code': 404
      });
      return;
    }

    // Format the response
    const response = {
      'request-id': requestId,
      'message': 'Success',
      'data': {
        'usage': {
          'total_requests': usageSummary.totalRequests,
          'last_request': usageSummary.lastRequest,
          'plan_type': usageSummary.planType,
          'limit': usageSummary.rateLimitLimit,
          'remaining': usageSummary.rateLimitRemaining,
          'reset_seconds': usageSummary.rateLimitReset,
          // Add a human-readable reset time
          'reset_time': usageSummary.rateLimitReset
            ? new Date(Date.now() + (usageSummary.rateLimitReset * 1000)).toISOString()
            : null
        }
      },
      'code': 0
    };

    res.status(200).json(response);
  } catch (error) {
    logger.error('Error getting RapidAPI user usage:', error instanceof Error ? error : new Error(String(error)));
    next(error instanceof Error ? error : new Error(String(error)));
  }
};
