import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { EmailModel, EmailAttachmentWithContent } from '../models/Email';
import { InboxModel } from '../models/Inbox';
import { createError, AppError } from '../middlewares/errorHandler';
import logger from '../utils/logger';
import { getCache, setCache, deleteCache, isRedisConnected } from '../config/redis-direct';
import { formatDate, formatTime } from '../utils/dateFormatter';
import { isContentTypeAllowed } from '../utils/attachmentValidator';
import { memoryCache } from '../middlewares/cacheMiddleware';

/**
 * Helper function to extract email address from formatted address string
 * @param formattedAddress - The formatted email address (e.g. "Display Name" <<EMAIL>>)
 * @returns The clean email address
 */
function extractEmailAddress(formattedAddress: string): string {
  if (!formattedAddress) return '';

  // Check if the address has the format "Name" <email>
  const emailMatch = formattedAddress.match(/<([^>]+)>/);
  if (emailMatch && emailMatch[1]) {
    return emailMatch[1]; // Return just the email part
  }

  // If no match found, return the original address (might already be clean)
  return formattedAddress;
}

/**
 * Generate cache key for emails list
 * @param inboxId - Inbox ID
 * @param page - Page number
 * @param size - Page size
 * @param filters - Filters applied
 * @returns Cache key
 */
const getEmailsListCacheKey = (inboxId: string, page: number, size: number, filters: Record<string, any>): string => {
  // Create a stable string representation of filters
  const filtersStr = Object.keys(filters).sort().map(key => `${key}:${filters[key]}`).join(',');
  return `emails:inbox:${inboxId}:page:${page}:size:${size}:filters:${filtersStr || 'none'}`;
};

/**
 * Cache TTL for emails list (5 minutes)
 */
const EMAILS_LIST_CACHE_TTL = 300;

/**
 * List emails for an inbox
 */
export const listEmails = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get request ID for logging
    const requestId = (req as any).requestId || uuidv4().toUpperCase();

    // Get inbox ID from params
    const inboxId = req.params.inboxId;

    // Validate inbox exists
    const inbox = await InboxModel.getById(inboxId);
    if (!inbox) {
      return next(createError.notFound('Inbox'));
    }

    // Get pagination parameters from validated query or defaults
    const validatedQuery = (req as any).validatedQuery || {};
    const page = validatedQuery.page || 1;
    const size = validatedQuery.size || 10;

    // Get filter parameters
    const filters: Record<string, any> = {};

    if (validatedQuery.from) {
      filters.from = validatedQuery.from;
    } else if (req.query.from) {
      // Fallback to req.query if validatedQuery is not available
      filters.from = req.query.from as string;
    }

    if (validatedQuery.subject) {
      filters.subject = validatedQuery.subject;
    } else if (req.query.subject) {
      filters.subject = req.query.subject as string;
    }

    if (validatedQuery.hasAttachments !== undefined) {
      filters.hasAttachments = validatedQuery.hasAttachments;
    } else if (req.query.hasAttachments !== undefined) {
      filters.hasAttachments = req.query.hasAttachments === 'true';
    }

    // CACHING DISABLED FOR INBOX MESSAGES ENDPOINT
    // Always fetch fresh data from the database to ensure read status is accurate

    // Get the original API key ID if available (for test API keys accessing other inboxes)
    const originalApiKeyId = (req as any).originalApiKeyId;

    // Only log in development mode
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Fetching fresh inbox messages for inbox ${inboxId} (caching disabled)`);
      logger.debug(`Using API key ID: ${req.apiKey?.id}, Original API key ID: ${originalApiKeyId || 'none'}`);
    }

    // Always get fresh data from the database
    const result = await EmailModel.getByInboxId(
      inboxId,
      page,
      size,
      filters,
      originalApiKeyId || req.apiKey?.id
    );

    // Set cache control headers to prevent browser caching
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Add a custom header to indicate this is a fresh response
    res.setHeader('X-Fresh-Data', 'true');

    // Prepare response with optimized mapping
    const response = {
      'request-id': requestId,
      'message': 'Success',
      'data': {
        'emails': result.emails.map((email: any) => {
          // Remove total_count from the response if it exists
          const { total_count, ...rest } = email;

          const emailResponse: any = {
            id: rest.id,
            from: {
              address: extractEmailAddress(rest.from_address),
              name: rest.from_name
            },
            to: extractEmailAddress(rest.to_address),
            subject: rest.subject || '(No Subject)',
            received_date: formatDate(rest.received_at),
            received_time: formatTime(rest.received_at),
            has_attachments: rest.has_attachments,
            is_read: rest.read_at !== null
          };

          // Include read date/time if available
          if (rest.read_at) {
            emailResponse.read_date = formatDate(rest.read_at);
            emailResponse.read_time = formatTime(rest.read_at);
          }

          return emailResponse;
        }),
        'pagination': {
          page: result.page,
          size: result.size,
          total: result.total,
          pages: result.pages
        }
      },
      'code': 0
    };

    // Add cache info in development mode
    if (process.env.NODE_ENV === 'development') {
      (response as any).cache = { disabled: true, fresh: true };
    }

    // Return response
    res.status(200).json(response);
  } catch (error) {
    next(error);
  }
};

/**
 * Cache TTL for individual email details (5 minutes)
 */
const EMAIL_DETAILS_CACHE_TTL = 300;

/**
 * Generate cache key for email details
 * @param inboxId - Inbox ID
 * @param emailId - Email ID
 * @returns Cache key
 */
const getEmailDetailsCacheKey = (inboxId: string, emailId: string): string => {
  return `email:${emailId}:inbox:${inboxId}`;
};

/**
 * Get a specific email
 */
export const getEmail = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get request ID for logging
    const requestId = (req as any).requestId || uuidv4().toUpperCase();

    // Get inbox and email IDs from params
    const inboxId = req.params.inboxId;
    const emailId = req.params.emailId;

    // Validate inbox exists
    const inbox = await InboxModel.getById(inboxId);
    if (!inbox) {
      return next(createError.notFound('Inbox'));
    }

    // Try to get email from cache if Redis is connected
    let email;
    let fromCache = false;
    const cacheKey = getEmailDetailsCacheKey(inboxId, emailId);

    if (isRedisConnected()) {
      const cachedEmail = await getCache<any>(cacheKey);
      if (cachedEmail) {
        email = cachedEmail;
        fromCache = true;
      }
    }

    // If not in cache, get from database
    if (!email) {
      email = await EmailModel.getById(emailId, inboxId);

      // Cache the result if Redis is connected
      if (isRedisConnected()) {
        await setCache(cacheKey, email, EMAIL_DETAILS_CACHE_TTL);
      }
    }

    // Mark email as read (even if from cache) and get the new read_at timestamp
    const readAt = await EmailModel.markAsRead(emailId, inboxId);

    // Update the email object with read status
    email.read_at = readAt;

    // Update the cache with the new read status
    if (isRedisConnected()) {
      await setCache(cacheKey, email, EMAIL_DETAILS_CACHE_TTL);

      // Invalidate all email list cache patterns to ensure read status is updated
      // Use more aggressive patterns to ensure complete cache invalidation
      const cachePatterns = [
        // Email list patterns
        `emails:inbox:${inboxId}:*`,                // Pattern used in EmailModel
        `cache:GET:/api/inboxes/${inboxId}/messages*`, // Pattern used by cacheMiddleware
        `email:${inboxId}:*`,                       // Additional pattern for safety
        `email:*:inbox:${inboxId}`,                 // Pattern used in getEmailDetailsCacheKey

        // More aggressive patterns to ensure complete invalidation
        `*${inboxId}*messages*`,                    // Any cache containing inbox ID and messages
        `*inboxes/${inboxId}*`                      // Any cache related to this inbox
      ];

      // Invalidate all cache patterns in parallel
      await Promise.all(cachePatterns.map(pattern => deleteCache(pattern)));

      // Also clear the memory cache for any entries related to this inbox's emails
      // Use a more aggressive approach to ensure all related cache entries are cleared
      const memoryKeys = Object.keys(memoryCache);

      // Clear ALL memory cache entries related to this inbox to ensure fresh data
      const emailListKeys = memoryKeys.filter(key =>
        // Standard patterns
        key.includes(`/api/inboxes/${inboxId}/messages`) ||
        key.includes(`emails:inbox:${inboxId}`) ||
        key.includes(`cache:/api/inboxes/${inboxId}/messages`) ||
        key.includes(`email:${inboxId}:`) ||

        // More aggressive patterns
        key.includes(`inboxes/${inboxId}`) ||
        key.includes(`${inboxId}`) ||
        key.includes('messages')
      );

      if (emailListKeys.length > 0) {
        if (process.env.NODE_ENV !== 'production') {
          logger.debug(`Clearing ${emailListKeys.length} related entries from memory cache`);

          // Log the first few keys being cleared for debugging
          if (emailListKeys.length > 0) {
            const keysToLog = emailListKeys.slice(0, Math.min(5, emailListKeys.length));
            logger.debug(`Sample keys being cleared: ${JSON.stringify(keysToLog)}`);
          }
        }

        // Delete all matching keys from memory cache
        emailListKeys.forEach(key => {
          delete memoryCache[key];
        });

        // Log success message
        if (process.env.NODE_ENV !== 'production') {
          logger.debug(`Successfully cleared ${emailListKeys.length} cache entries for inbox ${inboxId}`);
        }
      }
    }

    // Prepare email object
    const emailObject: any = {
      id: email.id,
      from: {
        address: extractEmailAddress(email.from_address),
        name: email.from_name
      },
      to: extractEmailAddress(email.to_address),
      subject: email.subject || '(No Subject)',
      text: email.text_content,
      html: email.html_content,
      received_date: formatDate(email.received_at),
      received_time: formatTime(email.received_at),
      has_attachments: email.has_attachments,
      attachments: email.attachments?.map((attachment: any) => ({
        filename: attachment.filename,
        content_type: attachment.content_type,
        size: attachment.size,
        is_inline: attachment.is_inline,
        content_id: attachment.content_id
        // Note: id is removed to prevent clients from trying to download attachments
      })) || []
    };

    // Always include read status information
    // If read_at is null, the email is unread
    emailObject.is_read = email.read_at !== null;

    // Include read date/time if available
    if (email.read_at) {
      emailObject.read_date = formatDate(email.read_at);
      emailObject.read_time = formatTime(email.read_at);
    }

    // Prepare response
    const response = {
      'request-id': requestId,
      'message': 'Success',
      'data': {
        'email': emailObject
      },
      'code': 0
    };

    // Add cache info in development mode
    if (process.env.NODE_ENV === 'development') {
      (response as any).cache = { hit: fromCache };
    }

    // Return response
    res.status(200).json(response);
  } catch (error) {
    next(error);
  }
};

/**
 * Get an email attachment
 * Note: This endpoint is currently disabled in the routes configuration.
 * Attachments are filtered for security but not available for download.
 */
export const getAttachment = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get inbox, email, and attachment IDs from params
    const inboxId = req.params.inboxId;
    const emailId = req.params.emailId;
    const attachmentId = req.params.attachmentId;

    // Validate inbox exists
    const inbox = await InboxModel.getById(inboxId);
    if (!inbox) {
      return next(createError.notFound('Inbox'));
    }

    // Get attachment
    const attachment: EmailAttachmentWithContent = await EmailModel.getAttachment(attachmentId, emailId);

    // Verify content type is allowed (double-check even if it was filtered during upload)
    if (!isContentTypeAllowed(attachment.content_type)) {
      logger.warn(`Blocked download of attachment with unsafe content type: ${attachment.content_type}`);
      return next(createError.forbidden('This attachment type is not allowed for security reasons'));
    }

    // Set content type header
    res.setHeader('Content-Type', attachment.content_type);

    // Always use attachment disposition for security (never inline)
    res.setHeader('Content-Disposition', `attachment; filename="${attachment.filename}"`);

    // Add security headers
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('Content-Security-Policy', "default-src 'none'");

    // Prevent caching of attachments
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    // Send attachment content
    res.send(attachment.content);
  } catch (error) {
    next(error);
  }
};

/**
 * Delete an email with enhanced cache invalidation
 */
export const deleteEmail = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get request ID for logging
    const requestId = (req as any).requestId || uuidv4().toUpperCase();

    // Get inbox and email IDs from params
    const inboxId = req.params.inboxId;
    const emailId = req.params.emailId;

    // Log the request parameters for debugging
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Deleting email with ID ${emailId} from inbox ${inboxId}`);
    }

    // Validate inbox exists
    const inbox = await InboxModel.getById(inboxId);
    if (!inbox) {
      return next(createError.notFound('Inbox'));
    }

    // Verify the email exists before attempting to delete it
    try {
      const email = await EmailModel.getById(emailId, inboxId);
      if (!email) {
        return next(createError.notFound('Email'));
      }
    } catch (emailError: any) {
      if (emailError.message && emailError.message.includes('not found')) {
        return next(createError.notFound('Email'));
      }
      throw emailError;
    }

    // Permanently delete email and its attachments
    await EmailModel.delete(emailId, inboxId);

    // Aggressive cache invalidation for this inbox's emails
    try {
      // Create an array of cache patterns to invalidate
      const cachePatterns = [
        // Email list caches
        `emails:inbox:${inboxId}:*`,          // All email lists for this inbox
        `cache:GET:/api/inboxes/${inboxId}/messages*`, // Any cached email list requests

        // Specific email caches
        `email:${inboxId}:${emailId}`,        // The specific email cache
        `cache:GET:/api/inboxes/${inboxId}/messages/${emailId}*`, // Any cached requests for this email

        // Broader patterns to ensure complete invalidation
        `cache:*${emailId}*`,                 // Any cache containing the email ID
        `email:*${emailId}*`                  // Any email cache containing the email ID
      ];

      // Invalidate all cache patterns in parallel
      await Promise.all(cachePatterns.map(pattern => deleteCache(pattern)));

      // Also clear the memory cache for any entries related to this email
      const memoryKeys = Object.keys(memoryCache);
      const emailKeys = memoryKeys.filter(key =>
        key.includes(emailId) ||
        key.includes(`/api/inboxes/${inboxId}/messages`)
      );

      if (emailKeys.length > 0) {
        if (process.env.NODE_ENV !== 'production') {
          logger.debug(`Clearing ${emailKeys.length} related entries from memory cache`);
        }
        emailKeys.forEach(key => {
          delete memoryCache[key];
        });
      }

      logger.debug(`Cache invalidated for inbox ${inboxId} and email ${emailId}`);
    } catch (cacheError: any) {
      // Log but don't fail if cache invalidation fails
      logger.error('Failed to invalidate cache:', cacheError);
    }

    // Verify the email was actually deleted
    try {
      const emailAfterDeletion = await EmailModel.getById(emailId, inboxId);
      if (emailAfterDeletion) {
        logger.error(`Email ${emailId} still exists after deletion attempt`);
        return next(new AppError('Failed to delete email', 500));
      }
    } catch (verifyError: any) {
      // If we get a "not found" error, that's good - it means the email was deleted
      if (!verifyError.message || !verifyError.message.includes('not found')) {
        logger.error('Error verifying email deletion:', verifyError);
      }
    }

    // Return response
    res.status(200).json({
      'request-id': requestId,
      'message': 'Email permanently deleted',
      'data': null,
      'code': 0
    });
  } catch (error) {
    // Cast the unknown error to Error type for the logger
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error('Error in deleteEmail:', err);
    next(error);
  }
};
