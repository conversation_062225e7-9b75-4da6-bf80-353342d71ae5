-- Verify that tables were created successfully
SELECT 
    table_name, 
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name) AS column_count
FROM 
    information_schema.tables t
WHERE 
    table_schema = 'public'
ORDER BY 
    table_name;

-- Count rows in each table
SELECT 'domains' AS table_name, COUNT(*) AS row_count FROM domains
UNION ALL
SELECT 'inboxes' AS table_name, COUNT(*) AS row_count FROM inboxes
UNION ALL
SELECT 'emails' AS table_name, COUNT(*) AS row_count FROM emails
UNION ALL
SELECT 'attachments' AS table_name, COUNT(*) AS row_count FROM attachments
UNION ALL
SELECT 'api_keys' AS table_name, COUNT(*) AS row_count FROM api_keys
UNION ALL
SELECT 'forwarding_verifications' AS table_name, COUNT(*) AS row_count FROM forwarding_verifications
UNION ALL
SELECT 'usage_logs' AS table_name, COUNT(*) AS row_count FROM usage_logs
UNION ALL
SELECT 'rapidapi_usage' AS table_name, COUNT(*) AS row_count FROM rapidapi_usage
ORDER BY table_name;
