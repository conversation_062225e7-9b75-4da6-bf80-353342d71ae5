import express from 'express';
import * as inboxController from '../controllers/inboxController';
import { validate, validateParams } from '../middlewares/validator';
import { createInboxSchema, inboxIdSchema } from '../validators/inboxValidators';
import { rapidApiAuth } from '../middlewares/rapidApiAuth';
import { generalRateLimiter } from '../middlewares/rateLimiter';
import { rapidApiRateLimiter } from '../middlewares/rapidApiRateLimiter';
import { inboxOwnershipAuth } from '../middlewares/inboxOwnershipAuth';
import { freeUserInboxLimiter } from '../middlewares/freeUserInboxLimiter';
import { cacheMiddleware } from '../middlewares/cacheMiddleware';
import { performanceMiddleware } from '../middlewares/performanceOptimizer';
import emailRoutes from './emailRoutes';
// import forwardingRoutes from './forwardingRoutes'; // Forwarding functionality disabled for now
import logger from '../utils/logger';

const router = express.Router();

// PHASE 2: Streamlined rate limiting for high-traffic inbox endpoints
router.use(rapidApiRateLimiter);
// PHASE 2: Skip general rate limiter for inbox endpoints (already covered by rapidApiRateLimiter)
// router.use(generalRateLimiter); // Commented out to reduce middleware overhead

// Apply authentication middleware that supports both RapidAPI and API key authentication
// This middleware first checks for RapidAPI authentication, and if that fails, it falls back to API key authentication
router.use(rapidApiAuth);

// POST /api/inboxes - Create a new inbox
// PHASE 2: Reduced performance middleware overhead for inbox creation
router.post('/',
  // PHASE 2: Removed performanceMiddleware array to reduce overhead
  validate(createInboxSchema),
  freeUserInboxLimiter,
  inboxController.createInbox
);

// GET /api/inboxes - List all inboxes (no caching to prevent inconsistent results)
// PHASE 2: Optimized for high-traffic - removed caching to fix issue with alternating empty/populated inbox lists
router.get('/', inboxController.listInboxes);

// DELETE /api/inboxes/:id - Delete an inbox
// Requires authentication (RapidAPI or API key) and ownership verification
// The rapidApiAuth middleware is already applied at the router level, so we don't need to apply it again
router.delete('/:id',
  validateParams(inboxIdSchema),
  inboxOwnershipAuth,
  inboxController.deleteInbox
);

// Mount email routes at /api/inboxes/:inboxId/messages
router.use('/:inboxId/messages', (req, res, next) => {
  // Log the inboxId parameter for debugging only in development
  if (process.env.NODE_ENV !== 'production') {
    logger.debug('Mounting email routes with inboxId:', req.params.inboxId);
  }
  next();
}, validateParams(inboxIdSchema), emailRoutes);

// Mount forwarding routes at /api/inboxes/:id/forwarding
// Forwarding functionality disabled for now
// router.use('/:id/forwarding', validateParams(inboxIdSchema), forwardingRoutes);

export default router;
