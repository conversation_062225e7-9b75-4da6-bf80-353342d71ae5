import { Request, Response, NextFunction } from 'express';
import { rateLimit } from 'express-rate-limit';
import { AppError } from './errorHandler';
import { isRedisConnected } from '../config/redis-direct';
import logger from '../utils/logger';

// Import tier limits from config
import { RAPIDAPI_TIER_LIMITS } from '../config/limits';

// Default configuration for rate limiters
const defaultRateLimitConfig = {
  windowMs: 30 * 24 * 60 * 60 * 1000, // 30 days (monthly limits)
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  message: {
    status: 'error',
    message: 'Too many requests, please try again later.',
    code: 429
  },
  handler: (req: Request, res: Response, next: NextFunction, options: any) => {
    // Log rate limit hit
    logger.warn(`Rate limit exceeded for ${(req as any).rapidApi?.user || req.ip}`);

    // Send error response
    res.status(options.statusCode).json(options.message);
  }
};

// Create a dynamic store based on Redis availability
const createDynamicStore = (prefix: string) => {
  // Use memory store only - Redis API doesn't support rate limiting yet
  return undefined;
};

// RapidAPI rate limiter middleware
export const rapidApiRateLimiter = rateLimit({
  ...defaultRateLimitConfig,
  store: createDynamicStore('rapidapi'),
  keyGenerator: (req: Request) => {
    // Use RapidAPI user as the rate limit key if available
    if ((req as any).rapidApi?.user) {
      return `rapidapi:${(req as any).rapidApi.user}`;
    }

    // Fall back to API key or IP address
    const apiKey = req.headers['x-api-key'] as string;
    if (apiKey) {
      return `api-key:${apiKey}`;
    }

    return `ip:${req.ip}`;
  },
  // Default to BASIC tier limit
  max: RAPIDAPI_TIER_LIMITS.BASIC,
  // Skip rate limiting for RapidAPI requests
  // RapidAPI handles its own rate limiting based on subscription tiers
  skip: (req: Request) => {
    // Skip for internal API requests
    if (req.headers['x-api-key'] === process.env.INTERNAL_API_KEY) {
      return true;
    }

    // PRIMARY CHECK: First check for a valid RapidAPI Proxy Secret (strongest authentication)
    const rapidApiProxySecret = (req.headers['x-rapidapi-proxy-secret'] ||
                               req.headers['X-RapidAPI-Proxy-Secret'] || '') as string;

    if (rapidApiProxySecret && process.env.RAPIDAPI_PROXY_SECRET &&
        rapidApiProxySecret === process.env.RAPIDAPI_PROXY_SECRET) {
      logger.info('Skipping RapidAPI rate limiting for request with valid Proxy Secret');
      return true;
    }

    // SECONDARY CHECK: If req.rapidApi is set by the rapidApiAuth middleware, it's a RapidAPI request
    if ((req as any).rapidApi) {
      logger.info('Skipping RapidAPI rate limiting for authenticated RapidAPI request');
      return true;
    }

    // We no longer check for standard RapidAPI headers without the Proxy Secret
    // This ensures that only requests with a valid Proxy Secret are allowed to bypass rate limiting

    // Don't skip rate limiting for other requests
    return false;
  }
});

// Export tier limits for use in other parts of the application
export { RAPIDAPI_TIER_LIMITS };
