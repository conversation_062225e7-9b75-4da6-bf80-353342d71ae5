import os from 'os';
import { Request, Response } from 'express';
import pool from '../config/database';
import logger from './logger';

// Store metrics for performance monitoring
const metrics = {
  requestCount: 0,
  errorCount: 0,
  startTime: Date.now(),
  lastMinuteRequests: [] as number[],
  lastMinuteErrors: [] as number[],
  responseTimeTotal: 0,
  responseTimeCount: 0,
};

// Update metrics every minute
setInterval(() => {
  // Keep only the last 60 entries (1 hour)
  if (metrics.lastMinuteRequests.length >= 60) {
    metrics.lastMinuteRequests.shift();
    metrics.lastMinuteErrors.shift();
  }
  
  // Add current minute's data
  metrics.lastMinuteRequests.push(metrics.requestCount);
  metrics.lastMinuteErrors.push(metrics.errorCount);
  
  // Reset counters
  metrics.requestCount = 0;
  metrics.errorCount = 0;
}, 60000);

/**
 * Increment request counter
 */
export const incrementRequestCount = (): void => {
  metrics.requestCount++;
};

/**
 * Increment error counter
 */
export const incrementErrorCount = (): void => {
  metrics.errorCount++;
};

/**
 * Record response time
 */
export const recordResponseTime = (responseTime: number): void => {
  metrics.responseTimeTotal += responseTime;
  metrics.responseTimeCount++;
};

/**
 * Get system health information
 */
export const getSystemHealth = async (): Promise<any> => {
  try {
    // Check database connection
    const dbStartTime = Date.now();
    const dbResult = await pool.query('SELECT 1');
    const dbResponseTime = Date.now() - dbStartTime;
    const dbStatus = dbResult.rowCount === 1 ? 'healthy' : 'unhealthy';
    
    // Calculate system metrics
    const uptime = Math.floor((Date.now() - metrics.startTime) / 1000);
    const memoryUsage = process.memoryUsage();
    const freeMemory = os.freemem();
    const totalMemory = os.totalmem();
    const cpuUsage = os.loadavg();
    
    // Calculate average response time
    const avgResponseTime = metrics.responseTimeCount > 0
      ? Math.round(metrics.responseTimeTotal / metrics.responseTimeCount)
      : 0;
    
    // Calculate requests per minute (average of last 5 minutes)
    const lastFiveMinutes = metrics.lastMinuteRequests.slice(-5);
    const requestsPerMinute = lastFiveMinutes.length > 0
      ? Math.round(lastFiveMinutes.reduce((sum, val) => sum + val, 0) / lastFiveMinutes.length)
      : metrics.requestCount;
    
    // Calculate error rate
    const lastFiveMinutesErrors = metrics.lastMinuteErrors.slice(-5);
    const errorsPerMinute = lastFiveMinutesErrors.length > 0
      ? Math.round(lastFiveMinutesErrors.reduce((sum, val) => sum + val, 0) / lastFiveMinutesErrors.length)
      : metrics.errorCount;
    
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: uptime,
      version: process.env.npm_package_version || '1.0.0',
      database: {
        status: dbStatus,
        responseTime: `${dbResponseTime}ms`,
      },
      system: {
        platform: process.platform,
        arch: process.arch,
        nodeVersion: process.version,
        cpuUsage: cpuUsage,
        memory: {
          free: `${Math.round(freeMemory / 1024 / 1024)}MB`,
          total: `${Math.round(totalMemory / 1024 / 1024)}MB`,
          usedPercent: `${Math.round((1 - freeMemory / totalMemory) * 100)}%`,
        },
        processMemory: {
          rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
          heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
          heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
          external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`,
        },
      },
      performance: {
        requestsPerMinute,
        errorsPerMinute,
        errorRate: requestsPerMinute > 0 ? `${Math.round((errorsPerMinute / requestsPerMinute) * 100)}%` : '0%',
        averageResponseTime: `${avgResponseTime}ms`,
      },
    };
  } catch (error) {
    logger.error('Health check failed', error as Error);
    return {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: (error as Error).message,
    };
  }
};

/**
 * Health check handler for Express
 */
export const healthCheckHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    const health = await getSystemHealth();
    
    // If any critical component is unhealthy, return 503
    const statusCode = health.status === 'healthy' ? 200 : 503;
    
    res.status(statusCode).json(health);
  } catch (error) {
    logger.error('Health check handler failed', error as Error);
    res.status(500).json({
      status: 'error',
      message: 'Health check failed',
      timestamp: new Date().toISOString(),
    });
  }
};

/**
 * Detailed metrics handler for Express
 */
export const metricsHandler = async (req: Request, res: Response): Promise<void> => {
  try {
    // Get basic health info
    const health = await getSystemHealth();
    
    // Add more detailed metrics
    const detailedMetrics = {
      ...health,
      metrics: {
        ...metrics,
        averageResponseTime: metrics.responseTimeCount > 0
          ? Math.round(metrics.responseTimeTotal / metrics.responseTimeCount)
          : 0,
        requestsHistory: metrics.lastMinuteRequests,
        errorsHistory: metrics.lastMinuteErrors,
      },
    };
    
    res.status(200).json(detailedMetrics);
  } catch (error) {
    logger.error('Metrics handler failed', error as Error);
    res.status(500).json({
      status: 'error',
      message: 'Metrics collection failed',
      timestamp: new Date().toISOString(),
    });
  }
};

export default {
  incrementRequestCount,
  incrementErrorCount,
  recordResponseTime,
  getSystemHealth,
  healthCheckHandler,
  metricsHandler,
};
