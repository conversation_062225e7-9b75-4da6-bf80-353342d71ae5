import logger from './logger';

/**
 * Validates that all required environment variables are set
 * Exits the process if any required variables are missing in production
 */
export const validateEnv = (): void => {
  // Define required environment variables for different environments
  const requiredVars = {
    all: [
      'PORT',
      'NODE_ENV',
      'PGHOST',
      'PGPORT',
      'PGDATABASE',
      'PGUSER',
      'PGPASSWORD',
    ],
    production: [
      'API_URL',
      'INTERNAL_API_KEY',
      'REDIS_API_URL',
      'REDIS_API_KEY',
    ],
    // Optional production variables that will be checked but won't cause the app to exit
    productionOptional: [
      // SMTP variables are completely optional and don't need warnings
      // 'SMTP_HOST',
      // 'SMTP_PORT',
      // 'SMTP_USER',
      // 'SMTP_PASS',
      // 'SMTP_FROM',
    ],
  };

  // Check common required variables
  const missingCommon = requiredVars.all.filter(v => !process.env[v]);

  if (missingCommon.length > 0) {
    const errorMsg = `Missing required environment variables: ${missingCommon.join(', ')}`;
    logger.error(errorMsg);

    // In production, exit the process
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    } else {
      // In development, just log a warning
      logger.warn('Application may not function correctly without these variables');
    }
  }

  // Check production-specific variables if in production
  if (process.env.NODE_ENV === 'production') {
    // Check required production variables
    const missingProd = requiredVars.production.filter(v => !process.env[v]);

    if (missingProd.length > 0) {
      const errorMsg = `Missing required production environment variables: ${missingProd.join(', ')}`;
      logger.error(errorMsg);
      process.exit(1);
    }

    // Check optional production variables and just warn if missing
    const missingOptional = requiredVars.productionOptional.filter(v => !process.env[v]);

    if (missingOptional.length > 0) {
      const warnMsg = `Missing optional production environment variables: ${missingOptional.join(', ')}`;
      logger.warn(warnMsg);
      logger.warn('Some features may not work correctly without these variables');
    }
  }

  // Warn about insecure SSL settings
  if (process.env.NODE_ENV === 'production' && process.env.NODE_TLS_REJECT_UNAUTHORIZED === '0') {
    logger.warn('SECURITY WARNING: NODE_TLS_REJECT_UNAUTHORIZED is set to 0, which disables SSL certificate validation');
  }

  // Warn about missing encryption key
  if (!process.env.ENCRYPTION_KEY) {
    logger.warn('SECURITY WARNING: ENCRYPTION_KEY is not set. A random key will be generated, which is not suitable for production.');
  }

  logger.info('Environment validation completed');
};

export default validateEnv;
