import express from 'express';
import { healthCheck, getMetrics, clearCache } from '../controllers/monitoringController';
import { apiKeyAuth } from '../middlewares/apiKeyAuth';
import { generalRateLimiter } from '../middlewares/rateLimiter';
import { rapidApiRateLimiter } from '../middlewares/rapidApiRateLimiter';
import { rapidApiAuth } from '../middlewares/rapidApiAuth';

const router = express.Router();

// Apply rate limiting to all monitoring routes
router.use(rapidApiRateLimiter);
router.use(generalRateLimiter);

// Health check endpoint - basic status (with optional RapidAPI auth)
router.get('/health', rapidApiAuth, healthCheck);

// Protected metrics endpoint - detailed metrics
router.get('/metrics', apiKeyAuth, getMetrics);

// Protected clear cache endpoint
router.post('/clear-cache', apiKeyAuth, clearCache);

export default router;
