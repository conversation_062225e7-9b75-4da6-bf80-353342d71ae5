import express from 'express';
import * as emailController from '../controllers/emailController';
import { rapidApiAuth } from '../middlewares/rapidApiAuth';
import { validateParams, validateQuery } from '../middlewares/validator';
import { emailValidators } from '../validators/emailValidators';
import { generalRateLimiter } from '../middlewares/rateLimiter';
import { inboxOwnershipAuth } from '../middlewares/inboxOwnershipAuth';
import { cacheMiddleware } from '../middlewares/cacheMiddleware';
import { noCacheMiddleware } from '../middlewares/noCacheMiddleware';
import logger from '../utils/logger';

const router = express.Router({ mergeParams: true });

// Apply rate limiting
router.use(generalRateLimiter);

// Apply authentication middleware that supports both RapidAPI and API key authentication
// This middleware first checks for RapidAPI authentication, and if that fails, it falls back to API key authentication
router.use(rapidApiAuth);

// Verify inbox ownership after authentication
router.use(inboxOwnershipAuth);

// List emails for an inbox WITHOUT caching
// Caching is completely disabled for this endpoint to ensure read status is always up-to-date
router.get(
  '/',
  validateQuery(emailValidators.listEmails),
  noCacheMiddleware, // Use the no-cache middleware instead of cacheMiddleware
  (req, res, next) => {
    // Add debug information in non-production environments
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Processing uncached inbox messages request for inbox: ${req.params.inboxId}`);
    }
    next();
  },
  emailController.listEmails
);

// Get a specific email with caching
// Use a longer TTL (300 seconds = 5 minutes) since individual emails don't change
router.get(
  '/:emailId',
  validateParams(emailValidators.getEmail),
  cacheMiddleware(300, req => {
    // Create a cache key that includes the inbox ID and email ID
    const inboxId = req.params.inboxId;
    const emailId = req.params.emailId;

    return `email:${inboxId}:${emailId}`;
  }),
  emailController.getEmail
);

// Get an email attachment - disabled for now
// router.get(
//   '/:emailId/attachments/:attachmentId',
//   validateParams(emailValidators.getAttachment),
//   emailController.getAttachment
// );

// Delete an email
router.delete(
  '/:emailId',
  validateParams(emailValidators.deleteEmail),
  (req, res, next) => {
    // Log the request parameters for debugging
    if (process.env.NODE_ENV !== 'production') {
      logger.debug(`Delete email request params: inboxId=${req.params.inboxId}, emailId=${req.params.emailId}, path=${req.path}, originalUrl=${req.originalUrl}`);
    }
    next();
  },
  emailController.deleteEmail
);

export default router;


