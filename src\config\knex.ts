import knex from 'knex';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Check if we're likely connecting to PgBouncer
const isPgBouncer = process.env.PGPORT === '6432' || process.env.USE_PGBOUNCER === 'true';

// Create connection configuration for CockroachDB
const connection = process.env.DATABASE_URL || {
  host: process.env.PGHOST,
  port: parseInt(process.env.PGPORT || '26257'),
  database: process.env.PGDATABASE,
  user: process.env.PGUSER,
  password: process.env.PGPASSWORD,
  ssl: process.env.PGSSLMODE === 'verify-full' ? {
    rejectUnauthorized: true
  } : process.env.PGSSLMODE === 'require' ? {
    rejectUnauthorized: false
  } : false,
};

// Create pool configuration optimized for CockroachDB
const poolConfig = {
  min: isPgBouncer ? 1 : parseInt(process.env.PG_MIN_CONNECTIONS || '3'),
  max: isPgBouncer ? 5 : parseInt(process.env.PG_MAX_CONNECTIONS || '15'),
  idleTimeoutMillis: isPgBouncer ? 10000 : parseInt(process.env.PG_IDLE_TIMEOUT || '30000'),
};

// Create knex instance
const db = knex({
  client: 'pg',
  connection,
  pool: poolConfig,
});

export default db;
